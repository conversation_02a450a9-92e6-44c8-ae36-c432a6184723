#!/usr/bin/env python3
"""
DSPy-based Log Anomaly Detection Agent for BGL Dataset with Random Selection
Uses Ollama with qwen:30b model and LabeledFewShot optimization
Processes all lines but randomly keeps only some in selected_anomalous and selected_normal
"""

import dspy
import pandas as pd
import re
from typing import List, Tuple, Dict
import random
from datetime import datetime


class LogAnomalySignature(dspy.Signature):
    """Classify a log entry as normal or anomalous based on its content."""
    
    log_entry = dspy.InputField(desc="The log entry content to analyze")
    classification = dspy.OutputField(desc="Classification: 'normal' or 'anomalous'")
    reasoning = dspy.OutputField(desc="Brief explanation for the classification")


class LogAnomalyDetector(dspy.Module):
    """DSPy module for detecting anomalies in log entries."""
    
    def __init__(self):
        super().__init__()
        self.classify = dspy.ChainOfThought(LogAnomalySignature)
    
    def forward(self, log_entry: str):
        """Forward pass through the module."""
        result = self.classify(log_entry=log_entry)
        return result


class BGLDatasetProcessor:
    """Processes the BGL dataset for training and evaluation with random selection."""

    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        self.data = []
    
    def parse_log_line(self, line: str) -> Dict:
        """Parse a single log line from BGL format."""
        parts = line.strip().split(' ', 6)
        if len(parts) < 7:
            return None
        
        alert_category = parts[0]
        timestamp = parts[1]
        date = parts[2]
        location = parts[3]
        detailed_timestamp = parts[4]
        node = parts[5]
        message = parts[6]
        
        # Label: "-" means normal (non-alert), anything else is anomalous (alert)
        is_anomalous = alert_category != "-"
        
        return {
            'alert_category': alert_category,
            'timestamp': timestamp,
            'date': date,
            'location': location,
            'detailed_timestamp': detailed_timestamp,
            'node': node,
            'message': message,
            'is_anomalous': is_anomalous,
            'label': 'anomalous' if is_anomalous else 'normal',
            'full_entry': line.strip()
        }
    
    def load_data_with_random_selection(self, keep_probability: float = 0.1) -> List[Dict]:
        """Read all lines but randomly keep only some in selected_anomalous and selected_normal."""
        print(f"Loading data from {self.dataset_path} with random selection...")
        print(f"Keep probability: {keep_probability}")

        selected_normal = []
        selected_anomalous = []
        total_lines = 0
        valid_lines = 0

        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f):
                total_lines += 1

                if line_num % 50000 == 0:
                    print(f"Processed {line_num} lines... Normal: {len(selected_normal)}, Anomalous: {len(selected_anomalous)}")

                parsed = self.parse_log_line(line)
                if parsed is None:
                    continue

                valid_lines += 1

                # Use random number generator to decide whether to keep this line
                if random.random() < keep_probability:
                    if parsed['is_anomalous']:
                        selected_anomalous.append(parsed)
                    else:
                        selected_normal.append(parsed)

        print(f"\nProcessing complete!")
        print(f"Total lines processed: {total_lines}")
        print(f"Valid lines: {valid_lines}")
        print(f"Selected normal samples: {len(selected_normal)}")
        print(f"Selected anomalous samples: {len(selected_anomalous)}")

        # Combine and shuffle all selected samples
        all_samples = selected_normal + selected_anomalous
        random.shuffle(all_samples)

        return all_samples
    
    def create_dspy_examples(self, data: List[Dict], for_training: bool = True) -> List[dspy.Example]:
        """Convert parsed data to DSPy examples."""
        examples = []
        for item in data:
            # Create a clean log entry without the alert category for classification
            log_parts = item['full_entry'].split(' ', 1)
            if len(log_parts) > 1:
                clean_entry = log_parts[1]  # Remove the alert category
            else:
                clean_entry = item['message']

            if for_training:
                # For training: include the correct classification and reasoning
                example = dspy.Example(
                    log_entry=clean_entry,
                    classification=item['label'],
                    reasoning=f"This log entry is {item['label']} based on its content and patterns."
                ).with_inputs('log_entry')
            else:
                # For testing: only include the log entry, no ground truth labels
                example = dspy.Example(
                    log_entry=clean_entry
                ).with_inputs('log_entry')
                # Store the ground truth separately for evaluation
                example.ground_truth_classification = item['label']

            examples.append(example)

        return examples


def setup_ollama_client(base_url: str = "http://192.168.0.105:11434"):
    """Setup Ollama client with qwen3:30b model."""
    print(f"Setting up Ollama client at {base_url}")
    lm = dspy.LM("ollama_chat/qwen3:30b", api_base="http://192.168.0.105:11434", temperature=0.1)
    # Configure DSPy to use Ollama
    
    dspy.configure(lm=lm)
    return lm


def train_and_evaluate():
    """Main training and evaluation function with random selection."""
    print("Starting Log Anomaly Detection Training with Random Selection...")

    # Setup Ollama
    setup_ollama_client()

    # Load and process data with random selection
    processor = BGLDatasetProcessor("dataset/BGL/BGL.log")
    sample_data = processor.load_data_with_random_selection(keep_probability=0.1)

    # Split selected data into train and test
    train_size = int(0.8 * len(sample_data))
    train_data = sample_data[:train_size]
    test_data = sample_data[train_size:]

    # Create examples with appropriate visibility of labels
    train_examples = processor.create_dspy_examples(train_data, for_training=True)
    test_examples = processor.create_dspy_examples(test_data, for_training=False)  # Test should not see labels

    print(f"Dataset split: {len(train_examples)} train, {len(test_examples)} test")
    
    # Initialize the model
    detector = LogAnomalyDetector()
    
    # Setup optimizer
    optimizer = dspy.LabeledFewShot(k=16)  # Use 16 examples for few-shot learning

    print("Optimizing model with LabeledFewShot...")
    optimized_detector = optimizer.compile(
        detector,
        trainset=train_examples,
    )
    
    # Evaluate on test set
    print("Evaluating on test set...")
    correct = 0
    total = 0

    # For F1-score and recall calculation
    true_positives = 0  # Correctly predicted anomalous
    false_positives = 0  # Incorrectly predicted anomalous
    false_negatives = 0  # Incorrectly predicted normal (missed anomalies)
    true_negatives = 0  # Correctly predicted normal

    for example in test_examples:
        try:
            prediction = optimized_detector(log_entry=example.log_entry)
            predicted_class = prediction.classification.lower().strip()
            actual_class = example.ground_truth_classification.lower().strip()

            if predicted_class == actual_class:
                correct += 1

            # Calculate confusion matrix components
            if actual_class == 'anomalous' and predicted_class == 'anomalous':
                true_positives += 1
            elif actual_class == 'normal' and predicted_class == 'anomalous':
                false_positives += 1
            elif actual_class == 'anomalous' and predicted_class == 'normal':
                false_negatives += 1
            elif actual_class == 'normal' and predicted_class == 'normal':
                true_negatives += 1

            total += 1
            if total % 1000 == 0:  # Progress update every 1000 examples
                print(f"Processed {total} test examples...")
            
            if total <= 5:  # Show first 5 predictions
                print(f"\nExample {total}:")
                print(f"Log: {example.log_entry[:100]}...")
                print(f"Actual: {actual_class}")
                print(f"Predicted: {predicted_class}")
                print(f"Reasoning: {prediction.reasoning}")
                print(f"Correct: {predicted_class == actual_class}")

        except Exception as e:
            print(f"Error processing example: {e}")
            continue

    # Calculate metrics
    accuracy = correct / total if total > 0 else 0
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

    print(f"\nFinal Results:")
    print(f"Accuracy: {accuracy:.3f} ({correct}/{total})")
    print(f"Precision: {precision:.3f}")
    print(f"Recall: {recall:.3f}")
    print(f"F1-Score: {f1_score:.3f}")
    print(f"\nConfusion Matrix:")
    print(f"True Positives (TP): {true_positives}")
    print(f"False Positives (FP): {false_positives}")
    print(f"False Negatives (FN): {false_negatives}")
    print(f"True Negatives (TN): {true_negatives}")
    
    return optimized_detector


if __name__ == "__main__":
    # Set random seed for reproducibility
    random.seed(42)
    
    # Train and evaluate the model
    trained_model = train_and_evaluate()
    
    print("\nTraining completed!")
