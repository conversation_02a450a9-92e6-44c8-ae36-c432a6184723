#!/usr/bin/env python3
"""
DSPy-based Log Anomaly Detection Agent for Thunderbird Dataset with BootstrapFewShotWithRandomSearch
Uses Ollama with qwen3:30b model and BootstrapFewShotWithRandomSearch optimization for training
Counts total lines and randomly selects lines for training and testing
"""

import dspy
import pandas as pd
import re
from typing import List, Tuple, Dict, Set
import random
from datetime import datetime
import os


class LogAnomalySignature(dspy.Signature):
    """Classify a log entry as normal or anomalous based on its content."""
    
    log_entry = dspy.InputField(desc="The log entry content to analyze")
    classification = dspy.OutputField(desc="Classification: 'normal' or 'anomalous'")
    reasoning = dspy.OutputField(desc="Brief explanation for the classification")


class LogAnomalyDetector(dspy.Module):
    """DSPy module for detecting anomalies in log entries."""
    
    def __init__(self):
        super().__init__()
        self.classify = dspy.ChainOfThought(LogAnomalySignature)
    
    def forward(self, log_entry: str):
        """Forward pass through the module."""
        result = self.classify(log_entry=log_entry)
        return result


class ThunderbirdRandomSamplingProcessor:
    """Processes the Thunderbird dataset with random line sampling."""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        self.total_lines = 0
        self.data = []
    
    def count_total_lines(self) -> int:
        """Count total lines in the dataset file."""
        print(f"Counting total lines in {self.dataset_path}...")
        
        line_count = 0
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 100000 == 0:
                    print(f"Counted {line_num} lines...")
                line_count = line_num
        
        self.total_lines = line_count
        print(f"Total lines in dataset: {self.total_lines:,}")
        return self.total_lines
    
    def parse_log_line(self, line: str) -> Dict:
        """Parse a single log line from Thunderbird format."""
        parts = line.strip().split(' ', 4)
        if len(parts) < 5:
            return None
        
        alert_category = parts[0]
        timestamp = parts[1]
        date = parts[2]
        node = parts[3]
        message = parts[4]
        
        # Label: "-" means normal (non-alert), anything else is anomalous (alert)
        is_anomalous = alert_category != "-"
        
        return {
            'alert_category': alert_category,
            'timestamp': timestamp,
            'date': date,
            'node': node,
            'message': message,
            'is_anomalous': is_anomalous,
            'label': 'anomalous' if is_anomalous else 'normal',
            'full_entry': line.strip()
        }
    
    def generate_random_line_numbers(self, num_samples: int) -> Set[int]:
        """Generate random line numbers to sample from the dataset."""
        if self.total_lines == 0:
            self.count_total_lines()
        
        if num_samples > self.total_lines:
            print(f"Warning: Requested {num_samples} samples but only {self.total_lines} lines available")
            num_samples = self.total_lines
        
        print(f"Generating {num_samples} random line numbers from {self.total_lines} total lines...")
        random_lines = set(random.sample(range(1, self.total_lines + 1), num_samples))
        
        return random_lines
    
    def load_stratified_random_sample(self, num_samples: int = 1000, balance_ratio: float = 0.3) -> List[Dict]:
        """Load stratified random samples to maintain class balance."""
        print(f"Loading {num_samples} stratified random samples (target {balance_ratio:.1%} anomalous)...")
        
        target_anomalous = int(num_samples * balance_ratio)
        target_normal = num_samples - target_anomalous
        
        # First pass: collect all line numbers by class
        normal_lines = []
        anomalous_lines = []
        
        print("First pass: identifying line numbers by class...")
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 100000 == 0:
                    print(f"Scanned {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is not None:
                    if parsed['is_anomalous']:
                        anomalous_lines.append(line_num)
                    else:
                        normal_lines.append(line_num)
        
        print(f"Found {len(normal_lines)} normal lines and {len(anomalous_lines)} anomalous lines")
        
        # Sample random line numbers from each class
        selected_normal_lines = set(random.sample(normal_lines, min(target_normal, len(normal_lines))))
        selected_anomalous_lines = set(random.sample(anomalous_lines, min(target_anomalous, len(anomalous_lines))))
        
        all_selected_lines = selected_normal_lines | selected_anomalous_lines
        
        print(f"Selected {len(selected_normal_lines)} normal and {len(selected_anomalous_lines)} anomalous line numbers")
        
        # Second pass: collect the actual data
        samples = []
        print("Second pass: collecting selected lines...")
        
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 100000 == 0:
                    print(f"Processed {line_num} lines, collected {len(samples)} samples...")
                
                if line_num in all_selected_lines:
                    parsed = self.parse_log_line(line)
                    if parsed is not None:
                        samples.append(parsed)
                
                # Early exit if we've collected all samples
                if len(samples) >= len(all_selected_lines):
                    break
        
        # Shuffle the collected samples
        random.shuffle(samples)
        
        # Print final statistics
        normal_count = sum(1 for item in samples if not item['is_anomalous'])
        anomalous_count = len(samples) - normal_count
        
        print(f"Final stratified sample: {len(samples)} total")
        print(f"  Normal: {normal_count} ({normal_count/len(samples)*100:.1f}%)")
        print(f"  Anomalous: {anomalous_count} ({anomalous_count/len(samples)*100:.1f}%)")
        
        return samples
    
    def create_dspy_examples(self, data: List[Dict], for_training: bool = True) -> List[dspy.Example]:
        """Convert parsed data to DSPy examples."""
        examples = []
        for item in data:
            # Create a clean log entry without the alert category for classification
            log_parts = item['full_entry'].split(' ', 1)
            if len(log_parts) > 1:
                clean_entry = log_parts[1]  # Remove the alert category
            else:
                clean_entry = item['message']
            
            if for_training:
                # For training: include the correct classification and reasoning
                example = dspy.Example(
                    log_entry=clean_entry,
                    classification=item['label'],
                    reasoning=f"This log entry is {item['label']} based on its content and patterns."
                ).with_inputs('log_entry')
            else:
                # For testing: only include the log entry, no ground truth labels
                example = dspy.Example(
                    log_entry=clean_entry
                ).with_inputs('log_entry')
                # Store the ground truth separately for evaluation
                example.ground_truth_classification = item['label']
            
            examples.append(example)
        
        return examples


def setup_ollama_client(base_url: str = "http://*************:11434"):
    """Setup Ollama client with qwen3:30b model."""
    print(f"Setting up Ollama client at {base_url}")
    lm = dspy.LM("ollama_chat/hf.co/unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF:Q2_K_XL", api_base="http://localhost:11434", temperature=0.1)
    # Configure DSPy to use Ollama
    
    dspy.configure(lm=lm)
    return lm


def create_evaluation_metric():
    """Create evaluation metric for BootstrapFewShotWithRandomSearch optimization."""
    def evaluate_accuracy(example, pred, trace=None):
        """Evaluate prediction accuracy."""
        try:
            predicted_class = pred.classification.lower().strip()
            actual_class = example.ground_truth_classification.lower().strip()
            return predicted_class == actual_class
        except:
            return False

    return evaluate_accuracy


def train_and_evaluate_bootstrap_random_search():
    """Main training and evaluation function with BootstrapFewShotWithRandomSearch optimization."""
    print("Starting Thunderbird Log Anomaly Detection with BootstrapFewShotWithRandomSearch...")

    # Setup Ollama
    setup_ollama_client()

    # Load and process data with random sampling
    processor = ThunderbirdRandomSamplingProcessor("datasets/Thunderbird/Thunderbird.log")

    # Count total lines first
    total_lines = processor.count_total_lines()

    # Load stratified random sample (maintains class balance)
    sample_data = processor.load_stratified_random_sample(num_samples=100, balance_ratio=0.3)

    # Split data into train, dev, and test sets for BootstrapFewShotWithRandomSearch
    train_size = int(0.6 * len(sample_data))  # 60% for training
    dev_size = int(0.2 * len(sample_data))    # 20% for development/validation
    test_size = len(sample_data) - train_size - dev_size  # 20% for testing

    train_data = sample_data[:train_size]
    dev_data = sample_data[train_size:train_size + dev_size]
    test_data = sample_data[train_size + dev_size:]

    # Create examples with appropriate visibility of labels
    train_examples = processor.create_dspy_examples(train_data, for_training=True)
    dev_examples = processor.create_dspy_examples(dev_data, for_training=False)  # Dev should not see labels during optimization
    test_examples = processor.create_dspy_examples(test_data, for_training=False)  # Test should not see labels

    print(f"Dataset split: {len(train_examples)} train, {len(dev_examples)} dev, {len(test_examples)} test")

    # Initialize the model
    detector = LogAnomalyDetector()

    # Create evaluation metric
    evaluate_accuracy = create_evaluation_metric()

    # Setup BootstrapFewShotWithRandomSearch optimizer
    print("Setting up BootstrapFewShotWithRandomSearch optimizer...")
    optimizer = dspy.BootstrapFewShotWithRandomSearch(
        metric=evaluate_accuracy,
        max_bootstrapped_demos=16,  # Maximum number of bootstrapped demonstrations
        max_labeled_demos=8,        # Maximum number of labeled demonstrations
        num_candidate_programs=10,  # Number of candidate programs to evaluate
        num_threads=4,              # Number of threads for parallel processing
        max_rounds=3,               # Maximum number of bootstrap rounds
    )

    print("Optimizing model with BootstrapFewShotWithRandomSearch...")
    optimized_detector = optimizer.compile(
        detector,
        trainset=train_examples,
        valset=dev_examples,  # Validation set for optimization
    )

    # Evaluate on test set
    print("Evaluating on test set...")
    correct = 0
    total = 0

    # For F1-score and recall calculation
    true_positives = 0  # Correctly predicted anomalous
    false_positives = 0  # Incorrectly predicted anomalous
    false_negatives = 0  # Incorrectly predicted normal (missed anomalies)
    true_negatives = 0  # Correctly predicted normal

    for example in test_examples:
        try:
            prediction = optimized_detector(log_entry=example.log_entry)
            predicted_class = prediction.classification.lower().strip()
            actual_class = example.ground_truth_classification.lower().strip()

            if predicted_class == actual_class:
                correct += 1

            # Calculate confusion matrix components
            if actual_class == 'anomalous' and predicted_class == 'anomalous':
                true_positives += 1
            elif actual_class == 'normal' and predicted_class == 'anomalous':
                false_positives += 1
            elif actual_class == 'anomalous' and predicted_class == 'normal':
                false_negatives += 1
            elif actual_class == 'normal' and predicted_class == 'normal':
                true_negatives += 1

            total += 1
            if total <= 5:  # Show first 5 predictions
                print(f"\nExample {total}:")
                print(f"Log: {example.log_entry[:100]}...")
                print(f"Actual: {actual_class}")
                print(f"Predicted: {predicted_class}")
                print(f"Reasoning: {prediction.reasoning}")
                print(f"Correct: {predicted_class == actual_class}")

        except Exception as e:
            print(f"Error processing example: {e}")
            continue

    # Calculate metrics
    accuracy = correct / total if total > 0 else 0
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

    print(f"\nFinal Results (BootstrapFewShotWithRandomSearch):")
    print(f"Total lines in dataset: {total_lines:,}")
    print(f"Samples used: {len(sample_data):,} ({len(sample_data)/total_lines*100:.3f}% of total)")
    print(f"Accuracy: {accuracy:.3f} ({correct}/{total})")
    print(f"Precision: {precision:.3f}")
    print(f"Recall: {recall:.3f}")
    print(f"F1-Score: {f1_score:.3f}")
    print(f"\nConfusion Matrix:")
    print(f"True Positives (TP): {true_positives}")
    print(f"False Positives (FP): {false_positives}")
    print(f"False Negatives (FN): {false_negatives}")
    print(f"True Negatives (TN): {true_negatives}")

    return optimized_detector


if __name__ == "__main__":
    # Set random seed for reproducibility
    random.seed(42)

    # Train and evaluate the model
    trained_model = train_and_evaluate_bootstrap_random_search()

    print("\nThunderbird BootstrapFewShotWithRandomSearch training completed!")
