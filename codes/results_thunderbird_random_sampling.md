Final stratified sample: 1000 total
  Normal: 700 (70.0%)
  Anomalous: 300 (30.0%)
Dataset split: 800 train, 200 test
Optimizing model with LabeledFewShot...
Evaluating on test set...

Example 1:
Log: 1132197962 2005.11.16 tbird-admin1 Nov 16 19:26:02 local@tbird-admin1 /apps/x86_64/system/ganglia-3....
Actual: normal
Predicted: normal
Reasoning: This log entry is normal based on its content and patterns.
Correct: True

Example 2:
Log: 1137003438 2006.01.11 bn57 Jan 11 10:17:18 bn57/bn57 kernel: Warning: acpi_table_parse(ACPI_SRAT) re...
Actual: normal
Predicted: normal
Reasoning: This log entry is normal based on its content and patterns.
Correct: True

Example 3:
Log: 1135223789 2005.12.21 an690 Dec 21 19:56:29 an690/an690 kernel: [KERNEL_IB][tsIbTavorMadProcess][/mn...
Actual: anomalous
Predicted: anomalous
Reasoning: This log entry is anomalous based on its content and patterns.
Correct: True

Example 4:
Log: 1140340591 2006.02.19 bn875 Feb 19 01:16:31 bn875/bn875 postfix/postdrop[30393]: warning: mail_queue...
Actual: normal
Predicted: anomalous
Reasoning: This log entry indicates a warning about a read-only file system, which is an abnormal condition affecting mail queue operations.
Correct: False

Example 5:
Log: 1135692061 2005.12.27 an512 Dec 27 06:01:01 an512/an512 crond(pam_unix)[23883]: session opened for u...
Actual: normal
Predicted: normal
Reasoning: This log entry indicates a normal session opening for the root user by the cron daemon, which is expected in system operations.
Correct: True

Final Results:
Total lines in dataset: 71,162,231
Samples used: 1,000 (0.001% of total)
Accuracy: 0.830 (166/200)
Precision: 0.638
Recall: 1.000
F1-Score: 0.779

Confusion Matrix:
True Positives (TP): 60
False Positives (FP): 34
False Negatives (FN): 0
True Negatives (TN): 106

Thunderbird random sampling training completed!