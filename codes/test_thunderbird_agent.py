#!/usr/bin/env python3
"""
Test script for the Thunderbird Log Anomaly Detection Agent
"""

import dspy
from thunderbird_anomaly_agent import LogAnomalyDetector, setup_ollama_client, ThunderbirdDatasetProcessor


def test_thunderbird_data_loading():
    """Test Thunderbird dataset loading."""
    print("Testing Thunderbird dataset loading...")
    try:
        processor = ThunderbirdDatasetProcessor("dataset/Thunderbird/Thunderbird.log")
        sample_data = processor.load_sample_data(num_samples=20, balance_ratio=0.5)
        
        print(f"Loaded {len(sample_data)} samples")
        
        # Show some examples
        normal_count = 0
        anomalous_count = 0
        
        for i, item in enumerate(sample_data[:5]):
            print(f"\nSample {i+1}:")
            print(f"Label: {item['label']}")
            print(f"Alert Category: {item['alert_category']}")
            print(f"Node: {item['node']}")
            print(f"Message: {item['message'][:100]}...")
            
            if item['label'] == 'normal':
                normal_count += 1
            else:
                anomalous_count += 1
        
        total_normal = sum(1 for item in sample_data if item['label'] == 'normal')
        total_anomalous = sum(1 for item in sample_data if item['label'] == 'anomalous')
        
        print(f"\nDataset composition:")
        print(f"Normal: {total_normal}")
        print(f"Anomalous: {total_anomalous}")
        
        return True
    except Exception as e:
        print(f"Data loading failed: {e}")
        return False


def test_thunderbird_classification():
    """Test basic classification on Thunderbird logs."""
    print("\nTesting Thunderbird classification...")
    try:
        setup_ollama_client()
        detector = LogAnomalyDetector()
        
        # Test with a normal-looking log entry (postfix warning)
        normal_log = "1131524071 2005.11.09 tbird-admin1 Nov 10 00:14:31 local@tbird-admin1 postfix/postdrop[10896]: warning: unable to look up public/pickup: No such file or directory"
        
        # Test with an anomalous-looking log entry (ECC memory error)
        anomalous_log = "1131674844 2005.11.10 cn994 Nov 10 18:07:24 cn994/cn994 Server Administrator: Instrumentation Service EventID: 1404 Memory device status is critical Memory device location: DIMM1_B Possible memory module event cause:Single bit error logging disabled"
        
        print("\nTesting normal log:")
        result1 = detector(log_entry=normal_log)
        print(f"Classification: {result1.classification}")
        print(f"Reasoning: {result1.reasoning}")
        
        print("\nTesting anomalous log:")
        result2 = detector(log_entry=anomalous_log)
        print(f"Classification: {result2.classification}")
        print(f"Reasoning: {result2.reasoning}")
        
        return True
    except Exception as e:
        print(f"Classification test failed: {e}")
        return False


def compare_datasets():
    """Compare BGL and Thunderbird dataset characteristics."""
    print("\nComparing BGL and Thunderbird datasets...")
    
    try:
        # Load small samples from both datasets
        from log_anomaly_agent import BGLDatasetProcessor
        
        bgl_processor = BGLDatasetProcessor("dataset/BGL/BGL.log")
        thunderbird_processor = ThunderbirdDatasetProcessor("dataset/Thunderbird/Thunderbird.log")
        
        bgl_sample = bgl_processor.load_sample_data(num_samples=50, balance_ratio=0.3)
        thunderbird_sample = thunderbird_processor.load_sample_data(num_samples=50, balance_ratio=0.3)
        
        print(f"\nBGL Dataset:")
        print(f"Sample size: {len(bgl_sample)}")
        bgl_normal = sum(1 for item in bgl_sample if item['label'] == 'normal')
        bgl_anomalous = sum(1 for item in bgl_sample if item['label'] == 'anomalous')
        print(f"Normal: {bgl_normal}, Anomalous: {bgl_anomalous}")
        
        print(f"\nThunderbird Dataset:")
        print(f"Sample size: {len(thunderbird_sample)}")
        tb_normal = sum(1 for item in thunderbird_sample if item['label'] == 'normal')
        tb_anomalous = sum(1 for item in thunderbird_sample if item['label'] == 'anomalous')
        print(f"Normal: {tb_normal}, Anomalous: {tb_anomalous}")
        
        # Show example alert categories
        print(f"\nBGL Alert Categories:")
        bgl_categories = set(item['alert_category'] for item in bgl_sample if item['is_anomalous'])
        print(f"Anomalous categories: {list(bgl_categories)[:5]}")
        
        print(f"\nThunderbird Alert Categories:")
        tb_categories = set(item['alert_category'] for item in thunderbird_sample if item['is_anomalous'])
        print(f"Anomalous categories: {list(tb_categories)[:5]}")
        
        return True
    except Exception as e:
        print(f"Dataset comparison failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=== Thunderbird Log Anomaly Detection Agent Tests ===\n")
    
    tests = [
        ("Thunderbird Data Loading", test_thunderbird_data_loading),
        ("Thunderbird Classification", test_thunderbird_classification),
        ("Dataset Comparison", compare_datasets)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    for test_name, passed in results.items():
        status = "PASS" if passed else "FAIL"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    print(f"\nOverall: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    return all_passed


if __name__ == "__main__":
    main()
