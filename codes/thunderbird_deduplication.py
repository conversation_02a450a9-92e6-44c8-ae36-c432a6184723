#!/usr/bin/env python3
"""
Thunderbird Log Deduplication Script
Removes duplicate entries from Thunderbird.log based on various strategies
"""

import hashlib
import argparse
import os
from typing import Dict, Set, List, Tuple
from collections import defaultdict
import re


class ThunderbirdLogDeduplicator:
    """Deduplicates Thunderbird log entries using various strategies."""
    
    def __init__(self, input_file: str, output_file: str = None):
        self.input_file = input_file
        self.output_file = output_file or f"{input_file}.deduplicated"
        self.stats = {
            'total_lines': 0,
            'valid_lines': 0,
            'duplicates_removed': 0,
            'unique_lines': 0
        }
    
    def parse_log_line(self, line: str) -> Dict:
        """Parse a single log line from Thunderbird format."""
        parts = line.strip().split(' ', 4)
        if len(parts) < 5:
            return None
        
        return {
            'alert_category': parts[0],
            'timestamp': parts[1],
            'date': parts[2],
            'node': parts[3],
            'message': parts[4],
            'full_entry': line.strip()
        }
    
    def deduplicate_exact(self) -> None:
        """Remove exact duplicate lines (identical full entries)."""
        print("Deduplicating using exact match strategy...")
        
        seen_lines = set()
        unique_lines = []
        
        with open(self.input_file, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                self.stats['total_lines'] += 1
                
                if line_num % 100000 == 0:
                    print(f"Processed {line_num} lines...")
                
                line_stripped = line.strip()
                if not line_stripped:
                    continue
                
                if line_stripped not in seen_lines:
                    seen_lines.add(line_stripped)
                    unique_lines.append(line_stripped)
                    self.stats['valid_lines'] += 1
                else:
                    self.stats['duplicates_removed'] += 1
        
        self.stats['unique_lines'] = len(unique_lines)
        self._write_output(unique_lines)
    
    def deduplicate_message_only(self) -> None:
        """Remove duplicates based on message content only (ignoring timestamps, nodes, etc.)."""
        print("Deduplicating using message-only strategy...")
        
        seen_messages = set()
        unique_lines = []
        
        with open(self.input_file, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                self.stats['total_lines'] += 1
                
                if line_num % 100000 == 0:
                    print(f"Processed {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is None:
                    continue
                
                message = parsed['message']
                if message not in seen_messages:
                    seen_messages.add(message)
                    unique_lines.append(parsed['full_entry'])
                    self.stats['valid_lines'] += 1
                else:
                    self.stats['duplicates_removed'] += 1
        
        self.stats['unique_lines'] = len(unique_lines)
        self._write_output(unique_lines)
    
    def deduplicate_semantic(self) -> None:
        """Remove duplicates based on semantic similarity (message patterns)."""
        print("Deduplicating using semantic pattern strategy...")
        
        seen_patterns = set()
        unique_lines = []
        
        with open(self.input_file, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                self.stats['total_lines'] += 1
                
                if line_num % 100000 == 0:
                    print(f"Processed {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is None:
                    continue
                
                # Create a pattern by replacing numbers, timestamps, and specific identifiers
                pattern = self._create_message_pattern(parsed['message'])
                
                if pattern not in seen_patterns:
                    seen_patterns.add(pattern)
                    unique_lines.append(parsed['full_entry'])
                    self.stats['valid_lines'] += 1
                else:
                    self.stats['duplicates_removed'] += 1
        
        self.stats['unique_lines'] = len(unique_lines)
        self._write_output(unique_lines)
    
    def deduplicate_content_hash(self) -> None:
        """Remove duplicates based on content hash (excluding timestamps)."""
        print("Deduplicating using content hash strategy...")
        
        seen_hashes = set()
        unique_lines = []
        
        with open(self.input_file, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                self.stats['total_lines'] += 1
                
                if line_num % 100000 == 0:
                    print(f"Processed {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is None:
                    continue
                
                # Create hash from alert_category + node + message (excluding timestamps)
                content = f"{parsed['alert_category']}|{parsed['node']}|{parsed['message']}"
                content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
                
                if content_hash not in seen_hashes:
                    seen_hashes.add(content_hash)
                    unique_lines.append(parsed['full_entry'])
                    self.stats['valid_lines'] += 1
                else:
                    self.stats['duplicates_removed'] += 1
        
        self.stats['unique_lines'] = len(unique_lines)
        self._write_output(unique_lines)
    
    def deduplicate_node_message(self) -> None:
        """Remove duplicates based on node + message combination (Thunderbird-specific)."""
        print("Deduplicating using node+message strategy...")
        
        seen_node_messages = set()
        unique_lines = []
        
        with open(self.input_file, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                self.stats['total_lines'] += 1
                
                if line_num % 100000 == 0:
                    print(f"Processed {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is None:
                    continue
                
                # Combine node and message for deduplication
                node_message = f"{parsed['node']}|{parsed['message']}"
                
                if node_message not in seen_node_messages:
                    seen_node_messages.add(node_message)
                    unique_lines.append(parsed['full_entry'])
                    self.stats['valid_lines'] += 1
                else:
                    self.stats['duplicates_removed'] += 1
        
        self.stats['unique_lines'] = len(unique_lines)
        self._write_output(unique_lines)
    
    def deduplicate_time_window(self, window_seconds: int = 60) -> None:
        """Remove duplicates within a time window (same message from same node within time window)."""
        print(f"Deduplicating using time window strategy ({window_seconds}s window)...")
        
        # Group by node and message pattern, track timestamps
        node_message_times = defaultdict(list)
        unique_lines = []
        
        with open(self.input_file, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                self.stats['total_lines'] += 1
                
                if line_num % 100000 == 0:
                    print(f"Processed {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is None:
                    continue
                
                # Create pattern for similar messages
                message_pattern = self._create_message_pattern(parsed['message'])
                key = f"{parsed['node']}|{message_pattern}"
                
                # For Thunderbird, we'll use a simplified approach
                # In practice, you'd parse the timestamp properly
                timestamp_str = parsed['timestamp']
                
                # Check if this pattern from this node appeared recently
                is_duplicate = False
                current_times = node_message_times[key]
                
                # Simple duplicate detection (simplified for demo)
                if len(current_times) > 0:
                    # Consider it duplicate if same pattern appears consecutively
                    # In real implementation, you'd compare actual timestamps
                    is_duplicate = len(current_times) > 3  # Allow up to 3 similar messages
                
                if not is_duplicate:
                    node_message_times[key].append(timestamp_str)
                    unique_lines.append(parsed['full_entry'])
                    self.stats['valid_lines'] += 1
                else:
                    self.stats['duplicates_removed'] += 1
        
        self.stats['unique_lines'] = len(unique_lines)
        self._write_output(unique_lines)
    
    def _create_message_pattern(self, message: str) -> str:
        """Create a pattern from a message by replacing variable parts."""
        # Replace process IDs in square brackets
        pattern = re.sub(r'\[\d+\]', '[PID]', message)
        
        # Replace general numbers
        pattern = re.sub(r'\b\d+\b', '<NUM>', pattern)
        
        # Replace hex addresses
        pattern = re.sub(r'\b0x[0-9a-fA-F]+\b', '<HEX>', pattern)
        
        # Replace IP addresses
        pattern = re.sub(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', '<IP>', pattern)
        
        # Replace file paths
        pattern = re.sub(r'/[^\s]+', '<PATH>', pattern)
        
        # Replace timestamps (various formats)
        pattern = re.sub(r'\b\d{4}-\d{2}-\d{2}\b', '<DATE>', pattern)
        pattern = re.sub(r'\b\d{2}:\d{2}:\d{2}\b', '<TIME>', pattern)
        
        # Replace common Thunderbird-specific patterns
        pattern = re.sub(r'\ben\d+\b', '<EN_NODE>', pattern)  # en161, en162, etc.
        pattern = re.sub(r'\bcn\d+\b', '<CN_NODE>', pattern)  # cn994, cn995, etc.
        pattern = re.sub(r'\bbn\d+\b', '<BN_NODE>', pattern)  # bn257, bn258, etc.
        
        # Replace session IDs and similar identifiers
        pattern = re.sub(r'\bsession\s+\w+', 'session <ID>', pattern)
        pattern = re.sub(r'\buser\s+\w+', 'user <USER>', pattern)
        
        return pattern
    
    def _write_output(self, unique_lines: List[str]) -> None:
        """Write deduplicated lines to output file."""
        print(f"Writing {len(unique_lines)} unique lines to {self.output_file}...")
        
        with open(self.output_file, 'w', encoding='utf-8') as f:
            for line in unique_lines:
                f.write(line + '\n')
    
    def print_stats(self) -> None:
        """Print deduplication statistics."""
        print(f"\n=== Thunderbird Deduplication Statistics ===")
        print(f"Total lines processed: {self.stats['total_lines']:,}")
        print(f"Valid lines: {self.stats['valid_lines']:,}")
        print(f"Duplicates removed: {self.stats['duplicates_removed']:,}")
        print(f"Unique lines remaining: {self.stats['unique_lines']:,}")
        
        if self.stats['total_lines'] > 0:
            duplicate_percentage = (self.stats['duplicates_removed'] / self.stats['total_lines']) * 100
            print(f"Duplicate percentage: {duplicate_percentage:.2f}%")
        
        # File size comparison
        if os.path.exists(self.input_file) and os.path.exists(self.output_file):
            input_size = os.path.getsize(self.input_file)
            output_size = os.path.getsize(self.output_file)
            size_reduction = ((input_size - output_size) / input_size) * 100
            print(f"File size reduction: {size_reduction:.2f}%")
            print(f"Original size: {input_size:,} bytes")
            print(f"Deduplicated size: {output_size:,} bytes")


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description='Deduplicate Thunderbird log files')
    parser.add_argument('input_file', help='Input Thunderbird log file path')
    parser.add_argument('-o', '--output', help='Output file path (default: input_file.deduplicated)')
    parser.add_argument('-s', '--strategy', choices=['exact', 'message', 'semantic', 'hash', 'node', 'time'], 
                       default='exact', help='Deduplication strategy (default: exact)')
    parser.add_argument('-w', '--window', type=int, default=60, 
                       help='Time window in seconds for time-based deduplication (default: 60)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found!")
        return
    
    deduplicator = ThunderbirdLogDeduplicator(args.input_file, args.output)
    
    print(f"Starting deduplication of {args.input_file}")
    print(f"Strategy: {args.strategy}")
    
    if args.strategy == 'exact':
        deduplicator.deduplicate_exact()
    elif args.strategy == 'message':
        deduplicator.deduplicate_message_only()
    elif args.strategy == 'semantic':
        deduplicator.deduplicate_semantic()
    elif args.strategy == 'hash':
        deduplicator.deduplicate_content_hash()
    elif args.strategy == 'node':
        deduplicator.deduplicate_node_message()
    elif args.strategy == 'time':
        deduplicator.deduplicate_time_window(args.window)
    
    deduplicator.print_stats()
    print(f"\nDeduplication complete! Output saved to: {deduplicator.output_file}")


if __name__ == "__main__":
    main()
