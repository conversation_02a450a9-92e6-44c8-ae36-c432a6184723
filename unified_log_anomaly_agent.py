#!/usr/bin/env python3
"""
Unified DSPy-based Log Anomaly Detection Agent
Combines BGL and Thunderbird dataset processing with LabeledFewShot optimization
Uses Ollama with qwen3:30b model
"""

import dspy
import pandas as pd
import re
from typing import List, Tuple, Dict
import random
from datetime import datetime
import os


class LogAnomalySignature(dspy.Signature):
    """Classify a log entry as normal or anomalous based on its content."""
    
    log_entry = dspy.InputField(desc="The log entry content to analyze")
    classification = dspy.OutputField(desc="Classification: 'normal' or 'anomalous'")
    reasoning = dspy.OutputField(desc="Brief explanation for the classification")


class LogAnomalyDetector(dspy.Module):
    """DSPy module for detecting anomalies in log entries."""
    
    def __init__(self):
        super().__init__()
        self.classify = dspy.ChainOfThought(LogAnomalySignature)
    
    def forward(self, log_entry: str):
        """Forward pass through the module."""
        result = self.classify(log_entry=log_entry)
        return result


class UnifiedDatasetProcessor:
    """Unified processor that handles both BGL and Thunderbird datasets."""
    
    def __init__(self, dataset_path: str, dataset_type: str = "auto"):
        self.dataset_path = dataset_path
        self.dataset_type = dataset_type
        self.data = []
        
        # Auto-detect dataset type if not specified
        if dataset_type == "auto":
            if "BGL" in dataset_path:
                self.dataset_type = "bgl"
            elif "Thunderbird" in dataset_path:
                self.dataset_type = "thunderbird"
            else:
                raise ValueError(f"Cannot auto-detect dataset type from path: {dataset_path}")
    
    def parse_log_line(self, line: str) -> Dict:
        """Parse a single log line from either BGL or Thunderbird format."""
        if self.dataset_type == "bgl":
            return self._parse_bgl_line(line)
        elif self.dataset_type == "thunderbird":
            return self._parse_thunderbird_line(line)
        else:
            raise ValueError(f"Unknown dataset type: {self.dataset_type}")
    
    def _parse_bgl_line(self, line: str) -> Dict:
        """Parse BGL format log line."""
        parts = line.strip().split(' ', 6)
        if len(parts) < 7:
            return None
        
        alert_category = parts[0]
        timestamp = parts[1]
        date = parts[2]
        location = parts[3]
        detailed_timestamp = parts[4]
        node = parts[5]
        message = parts[6]
        
        is_anomalous = alert_category != "-"
        
        return {
            'alert_category': alert_category,
            'timestamp': timestamp,
            'date': date,
            'location': location,
            'detailed_timestamp': detailed_timestamp,
            'node': node,
            'message': message,
            'is_anomalous': is_anomalous,
            'label': 'anomalous' if is_anomalous else 'normal',
            'full_entry': line.strip(),
            'dataset_type': 'bgl'
        }
    
    def _parse_thunderbird_line(self, line: str) -> Dict:
        """Parse Thunderbird format log line."""
        parts = line.strip().split(' ', 4)
        if len(parts) < 5:
            return None
        
        alert_category = parts[0]
        timestamp = parts[1]
        date = parts[2]
        node = parts[3]
        message = parts[4]
        
        is_anomalous = alert_category != "-"
        
        return {
            'alert_category': alert_category,
            'timestamp': timestamp,
            'date': date,
            'node': node,
            'message': message,
            'is_anomalous': is_anomalous,
            'label': 'anomalous' if is_anomalous else 'normal',
            'full_entry': line.strip(),
            'dataset_type': 'thunderbird'
        }
    
    def load_sample_data(self, num_samples: int = 1000, balance_ratio: float = 0.3) -> List[Dict]:
        """Load a balanced sample of the dataset."""
        print(f"Loading sample data from {self.dataset_path} ({self.dataset_type} format)...")
        
        normal_samples = []
        anomalous_samples = []
        
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f):
                if line_num % 10000 == 0:
                    print(f"Processed {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is None:
                    continue
                
                if parsed['is_anomalous']:
                    anomalous_samples.append(parsed)
                else:
                    normal_samples.append(parsed)
                
                # Stop when we have enough samples
                target_anomalous = int(num_samples * balance_ratio)
                target_normal = num_samples - target_anomalous
                
                if (len(anomalous_samples) >= target_anomalous and 
                    len(normal_samples) >= target_normal):
                    break
        
        # Balance the dataset
        selected_anomalous = random.sample(anomalous_samples, 
                                         min(target_anomalous, len(anomalous_samples)))
        selected_normal = random.sample(normal_samples, 
                                      min(target_normal, len(normal_samples)))
        
        all_samples = selected_anomalous + selected_normal
        random.shuffle(all_samples)
        
        print(f"Loaded {len(selected_normal)} normal and {len(selected_anomalous)} anomalous samples")
        return all_samples
    
    def create_dspy_examples(self, data: List[Dict], for_training: bool = True) -> List[dspy.Example]:
        """Convert parsed data to DSPy examples."""
        examples = []
        for item in data:
            # Create a clean log entry without the alert category for classification
            log_parts = item['full_entry'].split(' ', 1)
            if len(log_parts) > 1:
                clean_entry = log_parts[1]  # Remove the alert category
            else:
                clean_entry = item['message']
            
            if for_training:
                # For training: include the correct classification and reasoning
                example = dspy.Example(
                    log_entry=clean_entry,
                    classification=item['label'],
                    reasoning=f"This log entry is {item['label']} based on its content and patterns."
                ).with_inputs('log_entry')
            else:
                # For testing: only include the log entry, no ground truth labels
                example = dspy.Example(
                    log_entry=clean_entry
                ).with_inputs('log_entry')
                # Store the ground truth separately for evaluation
                example.ground_truth_classification = item['label']
                example.dataset_type = item['dataset_type']
            
            examples.append(example)
        
        return examples


def setup_ollama_client(base_url: str = "http://192.168.0.105:11434"):
    """Setup Ollama client with qwen3:30b model."""
    print(f"Setting up Ollama client at {base_url}")
    lm = dspy.LM("ollama_chat/qwen3:30b", api_base="http://192.168.0.105:11434", temperature=0.1)
    dspy.configure(lm=lm)
    return lm


def calculate_metrics(true_positives, false_positives, false_negatives, true_negatives, total, correct):
    """Calculate all evaluation metrics."""
    accuracy = correct / total if total > 0 else 0
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'tp': true_positives,
        'fp': false_positives,
        'fn': false_negatives,
        'tn': true_negatives,
        'total': total,
        'correct': correct
    }


def print_metrics(metrics, dataset_name):
    """Print formatted metrics."""
    print(f"\n{dataset_name} Results:")
    print(f"Accuracy: {metrics['accuracy']:.3f} ({metrics['correct']}/{metrics['total']})")
    print(f"Precision: {metrics['precision']:.3f}")
    print(f"Recall: {metrics['recall']:.3f}")
    print(f"F1-Score: {metrics['f1_score']:.3f}")
    print(f"\nConfusion Matrix:")
    print(f"True Positives (TP): {metrics['tp']}")
    print(f"False Positives (FP): {metrics['fp']}")
    print(f"False Negatives (FN): {metrics['fn']}")
    print(f"True Negatives (TN): {metrics['tn']}")


def evaluate_dataset(dataset_path: str, dataset_name: str, num_samples: int = 500):
    """Evaluate a single dataset."""
    print(f"\n{'='*60}")
    print(f"Training and Evaluating {dataset_name} Dataset")
    print('='*60)
    
    # Load and process data
    processor = UnifiedDatasetProcessor(dataset_path)
    sample_data = processor.load_sample_data(num_samples=num_samples, balance_ratio=0.3)
    
    # Split data into train and test only
    train_size = int(0.8 * len(sample_data))
    
    train_data = sample_data[:train_size]
    test_data = sample_data[train_size:]
    
    # Create examples with appropriate visibility of labels
    train_examples = processor.create_dspy_examples(train_data, for_training=True)
    test_examples = processor.create_dspy_examples(test_data, for_training=False)
    
    print(f"Dataset split: {len(train_examples)} train, {len(test_examples)} test")
    
    # Initialize and optimize the model
    detector = LogAnomalyDetector()
    optimizer = dspy.LabeledFewShot(k=16)
    
    print("Optimizing model with LabeledFewShot...")
    optimized_detector = optimizer.compile(detector, trainset=train_examples)
    
    # Evaluate on test set
    print("Evaluating on test set...")
    correct = 0
    total = 0
    
    # For F1-score and recall calculation
    true_positives = 0
    false_positives = 0
    false_negatives = 0
    true_negatives = 0
    
    for example in test_examples:
        try:
            prediction = optimized_detector(log_entry=example.log_entry)
            predicted_class = prediction.classification.lower().strip()
            actual_class = example.ground_truth_classification.lower().strip()
            
            if predicted_class == actual_class:
                correct += 1
            
            # Calculate confusion matrix components
            if actual_class == 'anomalous' and predicted_class == 'anomalous':
                true_positives += 1
            elif actual_class == 'normal' and predicted_class == 'anomalous':
                false_positives += 1
            elif actual_class == 'anomalous' and predicted_class == 'normal':
                false_negatives += 1
            elif actual_class == 'normal' and predicted_class == 'normal':
                true_negatives += 1
            
            total += 1
            
            print(f"\nExample {total}:")
            print(f"Log: {example.log_entry}")
            print(f"Actual: {actual_class}")
            print(f"Predicted: {predicted_class}")
            print(f"Correct: {predicted_class == actual_class}")
                
        except Exception as e:
            print(f"Error processing example: {e}")
            continue
    
    # Calculate and return metrics
    metrics = calculate_metrics(true_positives, false_positives, false_negatives, 
                              true_negatives, total, correct)
    print_metrics(metrics, dataset_name)
    
    return optimized_detector, metrics
