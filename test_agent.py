#!/usr/bin/env python3
"""
Test script for the Log Anomaly Detection Agent
"""

import dspy
from log_anomaly_agent import LogAnomalyDetector, setup_ollama_client, BGLDatasetProcessor


def test_ollama_connection():
    """Test connection to Ollama server."""
    print("Testing Ollama connection...")
    try:
        client = setup_ollama_client()

        # Simple test query using DSPy
        test_signature = dspy.Signature("question -> answer")
        test_module = dspy.Predict(test_signature)
        response = test_module(question="Hello, can you respond with 'Connection successful'?")
        print(f"Ollama response: {response.answer}")
        return True
    except Exception as e:
        print(f"Ollama connection failed: {e}")
        return False


def test_data_loading():
    """Test BGL dataset loading."""
    print("\nTesting BGL dataset loading...")
    try:
        processor = BGLDatasetProcessor("dataset/BGL/BGL.log")
        sample_data = processor.load_sample_data(num_samples=10, balance_ratio=0.5)
        
        print(f"Loaded {len(sample_data)} samples")
        
        # Show some examples
        for i, item in enumerate(sample_data[:3]):
            print(f"\nSample {i+1}:")
            print(f"Label: {item['label']}")
            print(f"Message: {item['message'][:100]}...")
            print(f"Alert Category: {item['alert_category']}")
        
        return True
    except Exception as e:
        print(f"Data loading failed: {e}")
        return False


def test_basic_classification():
    """Test basic classification without training."""
    print("\nTesting basic classification...")
    try:
        setup_ollama_client()
        detector = LogAnomalyDetector()
        
        # Test with a normal-looking log entry
        normal_log = "1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected"
        
        # Test with an anomalous-looking log entry  
        anomalous_log = "1117869872 2005.06.04 R23-M1-N8-I:J18-U11 2005-06-04-00.24.32.398284 R23-M1-N8-I:J18-U11 RAS APP FATAL ciod: failed to read message prefix on control stream"
        
        print("\nTesting normal log:")
        result1 = detector(log_entry=normal_log)
        print(f"Classification: {result1.classification}")
        print(f"Reasoning: {result1.reasoning}")
        
        print("\nTesting anomalous log:")
        result2 = detector(log_entry=anomalous_log)
        print(f"Classification: {result2.classification}")
        print(f"Reasoning: {result2.reasoning}")
        
        return True
    except Exception as e:
        print(f"Basic classification test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=== Log Anomaly Detection Agent Tests ===\n")
    
    tests = [
        ("Ollama Connection", test_ollama_connection),
        ("Data Loading", test_data_loading),
        ("Basic Classification", test_basic_classification)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    for test_name, passed in results.items():
        status = "PASS" if passed else "FAIL"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    print(f"\nOverall: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    return all_passed


if __name__ == "__main__":
    main()
