#!/usr/bin/env python3
"""
Simple DSPy-based Log Anomaly Detection Agent (No Optimization Training)
Uses Ollama with qwen3:30b model for direct classification without LabeledFewShot optimization
"""

import dspy
import pandas as pd
import re
from typing import List, Tuple, Dict
import random
from datetime import datetime


class LogAnomalySignature(dspy.Signature):
    """Classify a log entry as normal or anomalous based on its content."""
    
    log_entry = dspy.InputField(desc="The log entry content to analyze")
    classification = dspy.OutputField(desc="Classification: 'normal' or 'anomalous'")
    reasoning = dspy.OutputField(desc="Brief explanation for the classification")


class SimpleLogAnomalyDetector(dspy.Module):
    """Simple DSPy module for detecting anomalies in log entries without optimization."""
    
    def __init__(self):
        super().__init__()
        self.classify = dspy.ChainOfThought(LogAnomalySignature)
    
    def forward(self, log_entry: str):
        """Forward pass through the module."""
        result = self.classify(log_entry=log_entry)
        return result


class UniversalDatasetProcessor:
    """Universal processor that can handle both BGL and Thunderbird datasets."""
    
    def __init__(self, dataset_path: str, dataset_type: str = "auto"):
        self.dataset_path = dataset_path
        self.dataset_type = dataset_type
        self.data = []
        
        # Auto-detect dataset type if not specified
        if dataset_type == "auto":
            if "BGL" in dataset_path:
                self.dataset_type = "bgl"
            elif "Thunderbird" in dataset_path:
                self.dataset_type = "thunderbird"
            else:
                self.dataset_type = "bgl"  # default
    
    def parse_log_line(self, line: str) -> Dict:
        """Parse a single log line from either BGL or Thunderbird format."""
        if self.dataset_type == "bgl":
            return self._parse_bgl_line(line)
        elif self.dataset_type == "thunderbird":
            return self._parse_thunderbird_line(line)
        else:
            raise ValueError(f"Unknown dataset type: {self.dataset_type}")
    
    def _parse_bgl_line(self, line: str) -> Dict:
        """Parse BGL format log line."""
        parts = line.strip().split(' ', 6)
        if len(parts) < 7:
            return None
        
        alert_category = parts[0]
        timestamp = parts[1]
        date = parts[2]
        location = parts[3]
        detailed_timestamp = parts[4]
        node = parts[5]
        message = parts[6]
        
        is_anomalous = alert_category != "-"
        
        return {
            'alert_category': alert_category,
            'timestamp': timestamp,
            'date': date,
            'location': location,
            'detailed_timestamp': detailed_timestamp,
            'node': node,
            'message': message,
            'is_anomalous': is_anomalous,
            'label': 'anomalous' if is_anomalous else 'normal',
            'full_entry': line.strip(),
            'dataset_type': 'bgl'
        }
    
    def _parse_thunderbird_line(self, line: str) -> Dict:
        """Parse Thunderbird format log line."""
        parts = line.strip().split(' ', 4)
        if len(parts) < 5:
            return None
        
        alert_category = parts[0]
        timestamp = parts[1]
        date = parts[2]
        node = parts[3]
        message = parts[4]
        
        is_anomalous = alert_category != "-"
        
        return {
            'alert_category': alert_category,
            'timestamp': timestamp,
            'date': date,
            'node': node,
            'message': message,
            'is_anomalous': is_anomalous,
            'label': 'anomalous' if is_anomalous else 'normal',
            'full_entry': line.strip(),
            'dataset_type': 'thunderbird'
        }
    
    def load_sample_data(self, num_samples: int = 100, balance_ratio: float = 0.3) -> List[Dict]:
        """Load a balanced sample of the dataset."""
        print(f"Loading sample data from {self.dataset_path} ({self.dataset_type} format)...")
        
        normal_samples = []
        anomalous_samples = []
        
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f):
                if line_num % 10000 == 0 and line_num > 0:
                    print(f"Processed {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is None:
                    continue
                
                if parsed['is_anomalous']:
                    anomalous_samples.append(parsed)
                else:
                    normal_samples.append(parsed)
                
                # Stop when we have enough samples
                target_anomalous = int(num_samples * balance_ratio)
                target_normal = num_samples - target_anomalous
                
                if (len(anomalous_samples) >= target_anomalous and 
                    len(normal_samples) >= target_normal):
                    break
        
        # Balance the dataset
        selected_anomalous = random.sample(anomalous_samples, 
                                         min(target_anomalous, len(anomalous_samples)))
        selected_normal = random.sample(normal_samples, 
                                      min(target_normal, len(normal_samples)))
        
        all_samples = selected_anomalous + selected_normal
        random.shuffle(all_samples)
        
        print(f"Loaded {len(selected_normal)} normal and {len(selected_anomalous)} anomalous samples")
        return all_samples


def setup_ollama_client(base_url: str = "http://192.168.0.105:11434"):
    """Setup Ollama client with qwen3:30b model."""
    print(f"Setting up Ollama client at {base_url}")
    lm = dspy.LM("ollama_chat/qwen3:30b", api_base="http://192.168.0.105:11434", temperature=0.1)
    dspy.configure(lm=lm)
    return lm


def evaluate_simple_agent(dataset_path: str, dataset_type: str = "auto", num_samples: int = 50):
    """Evaluate the simple agent without optimization training."""
    print(f"Starting Simple Log Anomaly Detection Evaluation...")
    
    # Setup Ollama
    setup_ollama_client()
    
    # Load and process data
    processor = UniversalDatasetProcessor(dataset_path, dataset_type)
    sample_data = processor.load_sample_data(num_samples=num_samples, balance_ratio=0.3)
    
    print(f"Dataset: {len(sample_data)} samples")
    
    # Initialize the simple model (no optimization)
    detector = SimpleLogAnomalyDetector()
    
    # Evaluate directly on all samples
    print("Evaluating with simple agent (no optimization)...")
    correct = 0
    total = 0

    # For F1-score and recall calculation
    true_positives = 0  # Correctly predicted anomalous
    false_positives = 0  # Incorrectly predicted anomalous
    false_negatives = 0  # Incorrectly predicted normal (missed anomalies)
    true_negatives = 0  # Correctly predicted normal

    for i, item in enumerate(sample_data):
        try:
            # Create clean log entry without alert category
            log_parts = item['full_entry'].split(' ', 1)
            if len(log_parts) > 1:
                clean_entry = log_parts[1]  # Remove the alert category
            else:
                clean_entry = item['message']

            # Get prediction from the simple agent
            prediction = detector(log_entry=clean_entry)
            predicted_class = prediction.classification.lower().strip()
            actual_class = item['label'].lower().strip()

            if predicted_class == actual_class:
                correct += 1

            # Calculate confusion matrix components
            if actual_class == 'anomalous' and predicted_class == 'anomalous':
                true_positives += 1
            elif actual_class == 'normal' and predicted_class == 'anomalous':
                false_positives += 1
            elif actual_class == 'anomalous' and predicted_class == 'normal':
                false_negatives += 1
            elif actual_class == 'normal' and predicted_class == 'normal':
                true_negatives += 1

            total += 1

            if total <= 5:  # Show first 5 predictions
                print(f"\nExample {total}:")
                print(f"Dataset: {item['dataset_type']}")
                print(f"Log: {clean_entry[:100]}...")
                print(f"Actual: {actual_class}")
                print(f"Predicted: {predicted_class}")
                print(f"Reasoning: {prediction.reasoning}")
                print(f"Correct: {predicted_class == actual_class}")

        except Exception as e:
            print(f"Error processing example {total + 1}: {e}")
            continue

    # Calculate metrics
    accuracy = correct / total if total > 0 else 0
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

    print(f"\nSimple Agent Results:")
    print(f"Accuracy: {accuracy:.3f} ({correct}/{total})")
    print(f"Precision: {precision:.3f}")
    print(f"Recall: {recall:.3f}")
    print(f"F1-Score: {f1_score:.3f}")
    print(f"\nConfusion Matrix:")
    print(f"True Positives (TP): {true_positives}")
    print(f"False Positives (FP): {false_positives}")
    print(f"False Negatives (FN): {false_negatives}")
    print(f"True Negatives (TN): {true_negatives}")

    return detector, {'accuracy': accuracy, 'precision': precision, 'recall': recall, 'f1_score': f1_score}


def main():
    """Main function to test both datasets."""
    print("=== Simple Log Anomaly Detection Agent ===\n")
    
    # Set random seed for reproducibility
    random.seed(42)
    
    # Test on BGL dataset
    print("Testing on BGL dataset:")
    bgl_detector, bgl_metrics = evaluate_simple_agent(
        "dataset/BGL/BGL.log",
        dataset_type="bgl",
        num_samples=150
    )

    print("\n" + "="*60 + "\n")

    # Test on Thunderbird dataset
    print("Testing on Thunderbird dataset:")
    tb_detector, tb_metrics = evaluate_simple_agent(
        "dataset/Thunderbird/Thunderbird.log",
        dataset_type="thunderbird",
        num_samples=200
    )

    print(f"\n=== Final Comparison ===")
    print(f"BGL Simple Agent:")
    print(f"  Accuracy: {bgl_metrics['accuracy']:.3f}")
    print(f"  Precision: {bgl_metrics['precision']:.3f}")
    print(f"  Recall: {bgl_metrics['recall']:.3f}")
    print(f"  F1-Score: {bgl_metrics['f1_score']:.3f}")

    print(f"\nThunderbird Simple Agent:")
    print(f"  Accuracy: {tb_metrics['accuracy']:.3f}")
    print(f"  Precision: {tb_metrics['precision']:.3f}")
    print(f"  Recall: {tb_metrics['recall']:.3f}")
    print(f"  F1-Score: {tb_metrics['f1_score']:.3f}")

    print("\nSimple agent evaluation completed!")


if __name__ == "__main__":
    main()
