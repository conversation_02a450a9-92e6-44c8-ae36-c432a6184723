Starting Thunderbird Log Anomaly Detection Training...
Setting up Ollama client at http://192.168.0.105:11434
Loading sample data from dataset/Thunderbird/Thunderbird.log...
Processed 0 lines...
Processed 10000 lines...
Processed 20000 lines...
Processed 30000 lines...
Processed 40000 lines...
Processed 50000 lines...
Processed 60000 lines...
Processed 70000 lines...
Processed 80000 lines...
Processed 90000 lines...
Processed 100000 lines...
Processed 110000 lines...
Processed 120000 lines...
Processed 130000 lines...
Processed 140000 lines...
Processed 150000 lines...
Processed 160000 lines...
Processed 170000 lines...
Processed 180000 lines...
Processed 190000 lines...
Processed 200000 lines...
Processed 210000 lines...
Processed 220000 lines...
Processed 230000 lines...
Processed 240000 lines...
Processed 250000 lines...
Processed 260000 lines...
Processed 270000 lines...
Processed 280000 lines...
Processed 290000 lines...
Processed 300000 lines...
Processed 310000 lines...
Processed 320000 lines...
Processed 330000 lines...
Processed 340000 lines...
Processed 350000 lines...
Processed 360000 lines...
Processed 370000 lines...
Processed 380000 lines...
Processed 390000 lines...
Processed 400000 lines...
Processed 410000 lines...
Processed 420000 lines...
Processed 430000 lines...
Processed 440000 lines...
Processed 450000 lines...
Processed 460000 lines...
Processed 470000 lines...
Processed 480000 lines...
Processed 490000 lines...
Processed 500000 lines...
Processed 510000 lines...
Processed 520000 lines...
Processed 530000 lines...
Processed 540000 lines...
Processed 550000 lines...
Processed 560000 lines...
Processed 570000 lines...
Processed 580000 lines...
Processed 590000 lines...
Processed 600000 lines...
Processed 610000 lines...
Processed 620000 lines...
Processed 630000 lines...
Processed 640000 lines...
Processed 650000 lines...
Processed 660000 lines...
Processed 670000 lines...
Processed 680000 lines...
Processed 690000 lines...
Processed 700000 lines...
Processed 710000 lines...
Processed 720000 lines...
Processed 730000 lines...
Processed 740000 lines...
Processed 750000 lines...
Processed 760000 lines...
Processed 770000 lines...
Processed 780000 lines...
Processed 790000 lines...
Processed 800000 lines...
Processed 810000 lines...
Processed 820000 lines...
Processed 830000 lines...
Processed 840000 lines...
Processed 850000 lines...
Processed 860000 lines...
Processed 870000 lines...
Processed 880000 lines...
Processed 890000 lines...
Processed 900000 lines...
Processed 910000 lines...
Processed 920000 lines...
Processed 930000 lines...
Processed 940000 lines...
Processed 950000 lines...
Processed 960000 lines...
Processed 970000 lines...
Processed 980000 lines...
Processed 990000 lines...
Processed 1000000 lines...
Processed 1010000 lines...
Processed 1020000 lines...
Processed 1030000 lines...
Processed 1040000 lines...
Processed 1050000 lines...
Processed 1060000 lines...
Processed 1070000 lines...
Processed 1080000 lines...
Processed 1090000 lines...
Processed 1100000 lines...
Processed 1110000 lines...
Processed 1120000 lines...
Loaded 700 normal and 300 anomalous samples
Dataset split: 800 train, 200 test
Optimizing model with LabeledFewShot...
Evaluating on test set...

Example 1:
Log: 1131684372 2005.11.10 en161 Nov 10 20:46:12 en161/en161 sshd[4255]: Accepted publickey for root from...
Actual: normal
Predicted: normal
Reasoning: This log entry indicates a successful SSH public key authentication for the root user from a known IP address, which is a standard operation and not anomalous.
Correct: True

Example 2:
Log: 1131686643 2005.11.10 bn257 Nov 10 21:24:03 bn257/bn257 kernel: [KERNEL_IB][tsIbTavorMadProcess][/mn...
Actual: anomalous
Predicted: anomalous
Reasoning: This log entry is anomalous based on its content and patterns.
Correct: True

Example 3:
Log: 1131681745 2005.11.10 cn16 Nov 10 20:02:25 cn16/cn16 sshd(pam_unix)[3577]: session opened for user r...
Actual: normal
Predicted: normal
Reasoning: This log entry is normal based on its content and patterns.
Correct: True

Example 4:
Log: 1131597824 2005.11.09 tbird-admin1 Nov 9 20:43:44 local@tbird-admin1 /apps/x86_64/system/ganglia-3.0...
Actual: normal
Predicted: normal
Reasoning: This log entry is normal based on its content and patterns.
Correct: True

Example 5:
Log: 1131686140 2005.11.10 bn257 Nov 10 21:15:40 bn257/bn257 kernel: [KERNEL_IB][tsIbTavorMadProcess][/mn...
Actual: anomalous
Predicted: anomalous
Reasoning: This log entry is anomalous based on its content and patterns.
Correct: True

Final Results:
Accuracy: 0.900 (180/200)

Thunderbird training completed!