#!/usr/bin/env python3
"""
Test Ollama connection directly
"""

import requests
import json


def test_ollama_direct():
    """Test Ollama connection directly using HTTP requests."""
    base_url = "http://192.168.0.105:11434"
    
    print(f"Testing direct connection to Ollama at {base_url}")
    
    try:
        # Test if server is reachable
        print("1. Testing server availability...")
        response = requests.get(f"{base_url}/api/tags", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            models = response.json()
            print(f"   Available models: {[m['name'] for m in models.get('models', [])]}")
            
            # Test if qwen3:30b is available
            model_names = [m['name'] for m in models.get('models', [])]
            if 'qwen3:30b' in model_names:
                print("   ✓ qwen3:30b model is available")
                
                # Test generation
                print("2. Testing text generation...")
                generate_payload = {
                    "model": "qwen3:30b",
                    "prompt": "Classify this log entry as normal or anomalous: RAS KERNEL INFO instruction cache parity error corrected",
                    "stream": False
                }
                
                gen_response = requests.post(
                    f"{base_url}/api/generate",
                    json=generate_payload,
                    timeout=30
                )
                
                if gen_response.status_code == 200:
                    result = gen_response.json()
                    print(f"   ✓ Generation successful: {result.get('response', '')[:100]}...")
                    return True
                else:
                    print(f"   ✗ Generation failed: {gen_response.status_code}")
            else:
                print(f"   ✗ qwen3:30b not found. Available: {model_names}")
        else:
            print(f"   ✗ Server not accessible: {response.status_code}")
            
    except requests.exceptions.ConnectionError as e:
        print(f"   ✗ Connection error: {e}")
    except requests.exceptions.Timeout as e:
        print(f"   ✗ Timeout error: {e}")
    except Exception as e:
        print(f"   ✗ Unexpected error: {e}")
    
    return False


def test_litellm_ollama():
    """Test using LiteLLM with Ollama."""
    print("\nTesting LiteLLM with Ollama...")
    
    try:
        import litellm
        
        # Set the base URL for Ollama
        litellm.api_base = "http://192.168.0.105:11434"
        
        response = litellm.completion(
            model="ollama/qwen3:30b",
            messages=[{"role": "user", "content": "Say 'Hello from Ollama'"}],
            api_base="http://192.168.0.105:11434"
        )
        
        print(f"   ✓ LiteLLM response: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"   ✗ LiteLLM error: {e}")
        return False


if __name__ == "__main__":
    print("=== Ollama Connection Tests ===\n")
    
    direct_success = test_ollama_direct()
    litellm_success = test_litellm_ollama()
    
    print(f"\n=== Results ===")
    print(f"Direct Ollama: {'PASS' if direct_success else 'FAIL'}")
    print(f"LiteLLM Ollama: {'PASS' if litellm_success else 'FAIL'}")
    
    if not (direct_success or litellm_success):
        print("\n=== Troubleshooting ===")
        print("1. Check if Ollama server is running on 192.168.0.105:11434")
        print("2. Verify the qwen3:30b model is installed: ollama list")
        print("3. Check network connectivity to the server")
        print("4. Try: curl http://192.168.0.105:11434/api/tags")
